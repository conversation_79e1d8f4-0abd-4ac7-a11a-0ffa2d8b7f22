package com.yunhesoft.system.tools.diy.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.entity.BaseEntity;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.org.entity.po.SysOrg;
import com.yunhesoft.system.org.entity.vo.SysOrgTreeData;
import com.yunhesoft.system.org.service.ISysOrgService;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.tds.entity.dto.TdsExportDto;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tools.components.service.SysComponentService;
import com.yunhesoft.system.tools.diy.entity.dto.PageParamDto;
import com.yunhesoft.system.tools.diy.entity.dto.SavePageDto;
import com.yunhesoft.system.tools.diy.entity.dto.TemplateParamDto;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageLib;
import com.yunhesoft.system.tools.diy.entity.po.DiyPageLibRel;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibInfoVo;
import com.yunhesoft.system.tools.diy.entity.vo.DiyPageLibVo;
import com.yunhesoft.system.tools.diy.service.IDiyPageLibInfoService;
import com.yunhesoft.system.tools.diy.service.IDiyPageLibService;
import com.yunhesoft.system.tools.printer.service.PrinterService;

import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 * @Description: 保存自定义页面实现类$
 * @date 2022/3/17
 */
@Log4j2
@Service
public class DiyPageLibServiceImpl implements IDiyPageLibService {

	private static final String TDS_INDEX = "TDS_INDEX";
	@Autowired
	private EntityService dao;
	@Autowired
	private IDiyPageLibInfoService infoService;

	@Autowired
	private ISysOrgService iser;

	@Autowired
	private SysComponentService comServ;
	@Autowired
	private PrinterService printerServ;// 打印配置
	@Autowired
	private IDataSourceService tdsServ; // 数据源服务
	@Autowired
	public HttpServletResponse response;

	/**
	 * 查询页面
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	@Override
	public List<DiyPageLibVo> getPage(PageParamDto paramDto, Pagination<?> page) {
		Where where = Where.create();
		where.eq(DiyPageLib::getUsed, 1);
		if (StringUtils.isNotEmpty(paramDto.getName())) {
			where.like(DiyPageLib::getTitle, paramDto.getName());
		}
		if (StringUtils.isNotEmpty(paramDto.getType())) {
			where.eq(DiyPageLib::getObjType, paramDto.getType());
		}
		if (StringUtils.isNotEmpty(paramDto.getPageId())) {
			where.eq(DiyPageLib::getPageId, paramDto.getPageId());
		}
		Order order = Order.create();
		order.orderByAsc(DiyPageLib::getTmSort);
		List<DiyPageLib> list;
		if (ObjUtils.notEmpty(page)) {
			list = dao.queryData(DiyPageLib.class, where, order, page);
		} else {
			list = dao.rawQueryListByWhere(DiyPageLib.class, where, order);
		}

		List<DiyPageLibVo> listVo = new ArrayList<>();
		if (StringUtils.isNotEmpty(list)) {
			for (DiyPageLib p : list) {
				DiyPageLibVo bean = ObjUtils.copyTo(p, DiyPageLibVo.class);
				listVo.add(bean);
			}
		}
		return listVo;
	}

	/**
	 * 保存自定义页面
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	@Override
	public Boolean savePages(SavePageDto param) {
		Boolean bln = false;
		if (ObjUtils.notEmpty(param)) {
			if (StringUtils.isNotEmpty(param.getData())) {
				List<DiyPageLib> insertList = new ArrayList<>();
				List<DiyPageLib> updateList = new ArrayList<>();
				List<DiyPageLib> deleteList = new ArrayList<>();
				for (DiyPageLibVo page : param.getData()) {
					Integer rowFlag = page.getRowFlag();
					DiyPageLib bean = ObjUtils.copyTo(page, DiyPageLib.class);
					if (ObjUtils.notEmpty(bean)) {
						if (rowFlag == null || rowFlag == 0) {
							// 添加
							insertList.add(bean);
						} else if (rowFlag == 1) {
							// 修改
							updateList.add(bean);
						} else {
							// 删除
							deleteList.add(bean);
						}
					}
				}
				if (StringUtils.isNotEmpty(insertList)) {
					bln = this.addPage(insertList);
				}
				if (StringUtils.isNotEmpty(updateList)) {
					bln = this.upPage(updateList);
				}
				if (StringUtils.isNotEmpty(deleteList)) {
					bln = this.delPage(deleteList);
				}
			}
		}
		return bln;
	}

	/**
	 * 添加自定义页面
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private Boolean addPage(List<DiyPageLib> insertList) {
		int msg = 0;
		Where where = Where.create();
		Integer max = dao.findMaxValue(DiyPageLib.class, DiyPageLib::getTmSort, Integer.class, where);
		if (ObjUtils.isEmpty(max)) {
			max = 0;
		}
		if (StringUtils.isNotEmpty(insertList)) {
			for (DiyPageLib page : insertList) {
				page.setId(TMUID.getUID());
				page.setPageId(page.getId());
				page.setObjType("zero");
				page.setUsed(1);
				page.setTmSort(++max);
			}
			msg = dao.insertBatch(insertList);
		}
		return msg > 0;
	}

	/**
	 * 删除自定义页面
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private Boolean delPage(List<DiyPageLib> deleteList) {
		int msg = 0;
		if (StringUtils.isNotEmpty(deleteList)) {
			for (DiyPageLib page : deleteList) {
				page.setUsed(0);
			}
			msg = dao.updateByIdBatch(deleteList);
		}
		return msg > 0;
	}

	/**
	 * 修改自定义页面
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private Boolean upPage(List<DiyPageLib> updateList) {
		int msg = 0;
		if (StringUtils.isNotEmpty(updateList)) {
			for (DiyPageLib page : updateList) {
				page.setUsed(1);
			}
			msg = dao.updateByIdBatch(updateList);
		}
		return msg > 0;
	}

	/**
	 * 模板操作 根据 pageId进行 复制引用
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public void operTemplate(TemplateParamDto param) {
		List<DiyPageLib> insertList = new ArrayList<>();
		DiyPageLib copyLib = new DiyPageLib();
		DiyPageLib lib = new DiyPageLib();
		if (param.getModel() == 0) {
			// 复制de 目标id
			String newId = param.getTargetPageId();
			String oldId = param.getSrcPageId();
			// 复制 库
			Where where = Where.create();
			Integer max = dao.findMaxValue(DiyPageLib.class, DiyPageLib::getTmSort, Integer.class, where);
			if (ObjUtils.isEmpty(max)) {
				max = 0;
			}
			// 全部库
			where.eq(DiyPageLib::getUsed, 1);
			List<DiyPageLib> allLib = dao.rawQueryListByWhere(DiyPageLib.class, where);
			List<String> allLibId = new ArrayList<>();
			if (StringUtils.isNotEmpty(allLib)) {
				allLibId = allLib.stream().map(BaseEntity::getId).collect(Collectors.toList());
			}
			// 复制库
			if ("zero".equals(param.getType())) {
				Where where1 = Where.create();
				where1.eq(DiyPageLib::getId, oldId);
				where1.eq(DiyPageLib::getUsed, 1);
				lib = dao.rawQueryObjectByWhere(DiyPageLib.class, where1);
			} else {
				DiyPageLibRel rel = null;
				if ("zero".equals(param.getSrcType())) {
					// 复制的源 属于自定义页面 本身不附带映射关系
					rel = new DiyPageLibRel();
					rel.setPageId(oldId);
				} else {
					Where where1 = Where.create();
					where1.eq(DiyPageLibRel::getObjId, oldId);
					// 获取lib 再复制
					rel = dao.rawQueryObjectByWhere(DiyPageLibRel.class, where1);
				}
				if (ObjUtils.notEmpty(rel)) {
					Where where4 = Where.create();
					if (rel.getPageId().contains(",")) {
						where4.in(DiyPageLib::getId, rel.getPageId().split(","));
					} else {
						where4.eq(DiyPageLib::getId, rel.getPageId());
					}
					where4.eq(DiyPageLib::getUsed, 1);
					lib = dao.rawQueryObjectByWhere(DiyPageLib.class, where4);
				}
			}
			if (ObjUtils.notEmpty(lib)) {
				copyLib = ObjUtils.copyTo(lib, DiyPageLib.class);
				copyLib.setId(newId);
				copyLib.setPageId(newId);
				copyLib.setTitle(param.getName());
				copyLib.setObjType(param.getType());
				copyLib.setTmSort(++max);
				copyLib.setUsed(1);
//                insertList.add(copyLib);
				if (allLibId.contains(newId)) {
					dao.updateById(copyLib);
				} else {
					dao.insert(copyLib);
				}
			}
			if (!"zero".equals(param.getType())) {
				// 维护库
				// 复制关系
				List<DiyPageLibRel> rels = null;
				if ("zero".equals(param.getSrcType())) {
					// 源页面没有映射关系属于自定义页面的情况 关系表中查不到
					rels = new ArrayList<>();
					DiyPageLibRel r = new DiyPageLibRel();
					r.setPageId(oldId);
					r.setIsRefs(0);
					rels.add(r);
				} else {
					Where where2 = Where.create();
					where2.eq(DiyPageLibRel::getObjId, oldId);
					rels = dao.rawQueryListByWhere(DiyPageLibRel.class, where2);
				}
				if (StringUtils.isNotEmpty(rels)) {
					DiyPageLibRel copyRel = ObjUtils.copyTo(rels.get(0), DiyPageLibRel.class);
					copyRel.setId(TMUID.getUID());
					copyRel.setObjId(param.getObjId());
					copyRel.setPageId(newId);
					copyRel.setType(param.getType());
					// 删除旧关系
					Where where3 = Where.create();
					where3.eq(DiyPageLibRel::getObjId, param.getObjId());
					List<DiyPageLibRel> list = dao.rawQueryListByWhere(DiyPageLibRel.class, where3);
					if (StringUtils.isNotEmpty(list)) {
						dao.deleteByIdBatch(list);
					}
					dao.insert(copyRel);
					oldId = rels.get(0).getPageId();
				}
			}
			// 复制页面
			infoService.copyPage(oldId, newId);
		} else if (param.getModel() == 1) {
			// 保存引用关系
			Where where = Where.create();
			where.eq(DiyPageLibRel::getObjId, param.getTargetPageId());
			DiyPageLibRel rel = dao.queryObject(DiyPageLibRel.class, where);
			if (ObjUtils.notEmpty(rel)) {
				// 存在关系
				rel.setIsRefs(1);
				rel.setPageId(null);
				rel.setRefsPageId(param.getSrcPageId());
				if ("zero".equals(param.getRelType())) {
					rel.setRefsPageName(param.getName());
					rel.setPageId(param.getTargetPageId());
				}
				// 机构关系
				if ("first".equals(param.getRelType())) {
					SysOrg orgByOrgcode = iser.getOrgByOrgcode(param.getSrcPageId());
					if (ObjUtils.notEmpty(orgByOrgcode)) {
						rel.setRefsPageName(orgByOrgcode.getOrgname());
					}
				}
				// 岗位关系
				if ("second".equals(param.getRelType())) {
					List<SysPost> postList = comServ.getPostById(param.getSrcPageId());
					if (StringUtils.isNotEmpty(postList)) {
						rel.setRefsPageName(postList.get(0).getName());
					}
				}
				// 人员关系
				if ("third".equals(param.getRelType())) {
					List<SysEmployeeInfo> employeeList = comServ.getEmployeeById(param.getSrcPageId());
					if (StringUtils.isNotEmpty(employeeList)) {
						rel.setRefsPageName(employeeList.get(0).getEmpname());
					}
				}
				dao.updateById(rel);
			} else {
				// 不存在关系 新增关系
				DiyPageLibRel newRel = new DiyPageLibRel();
				newRel.setId(TMUID.getUID());
				newRel.setType(param.getType());
				newRel.setObjId(param.getObjId());
				newRel.setPageId(null);
				newRel.setIsRefs(1);
				newRel.setRefsPageId(param.getSrcPageId());
				if ("zero".equals(param.getRelType())) {
					newRel.setRefsPageName(param.getName());
				}
				// 机构关系
				if ("first".equals(param.getRelType())) {
					SysOrg orgByOrgcode = iser.getOrgByOrgcode(param.getSrcPageId());
					if (ObjUtils.notEmpty(orgByOrgcode)) {
						newRel.setRefsPageName(orgByOrgcode.getOrgname());
					}
				}
				// 岗位关系
				if ("second".equals(param.getRelType())) {
					List<SysPost> postList = comServ.getPostById(param.getSrcPageId());
					if (StringUtils.isNotEmpty(postList)) {
						newRel.setRefsPageName(postList.get(0).getName());
					}
				}
				// 人员关系
				if ("third".equals(param.getRelType())) {
					List<SysEmployeeInfo> employeeList = comServ.getEmployeeById(param.getSrcPageId());
					if (StringUtils.isNotEmpty(employeeList)) {
						newRel.setRefsPageName(employeeList.get(0).getEmpname());
					}
				}
				dao.insert(newRel);
			}
		}

	}

	/**
	 * 映射page
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public JSONObject redirectPage(String reqPageId, String type) {
		JSONObject obj = new JSONObject();
		Where where = Where.create();
		if ("zero".equals(type)) {
			where.eq(DiyPageLibRel::getPageId, reqPageId);
		} else {
			where.eq(DiyPageLibRel::getObjId, reqPageId);
		}
		List<DiyPageLibRel> relations = dao.rawQueryListByWhere(DiyPageLibRel.class, where);
		List<DiyPageLibRel> allRelations = dao.queryList(DiyPageLibRel.class);
		if (StringUtils.isNotEmpty(relations)) {
			DiyPageLibRel rel = relations.get(0);
			if (rel.getIsRefs() == 1) {
				// 已引用
				obj.put("pageId", rel.getRefsPageId());
				obj.put("type", rel.getType());
				List<DiyPageLibRel> list = allRelations.stream().filter(
						rel1 -> StringUtils.isNotEmpty(rel1.getObjId()) && rel1.getObjId().equals(rel.getRefsPageId()))
						.collect(Collectors.toList());
				if (list.size() == 0) {
					// 没有嵌套引用，
					obj.put("type", "zero");
				}
				return obj;
			}
		} else {
			return null;
		}
		return null;
	}

	/**
	 * 删除引用 设为不继承 和 更改继承方式的时候需要调用
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public Boolean deleteRelationByReq(String reqId) {
		int msg = 0;
		Where where = Where.create();
		where.eq(DiyPageLibRel::getObjId, reqId);
		DiyPageLibRel rel = dao.rawQueryObjectByWhere(DiyPageLibRel.class, where);
		if (ObjUtils.isEmpty(rel)) {
			return false;
		}
		rel.setIsRefs(0);
		rel.setRefsPageId("");
		rel.setRefsPageName("");
		rel.setExtendsType(0);
		msg = dao.updateById(rel);
		return msg > 0;
	}

	/**
	 * 指定机构继承
	 *
	 * @return 页面详情
	 * <AUTHOR>
	 * @params 继承的机构编码
	 */
	@Override
	public List<DiyPageLibInfoVo> extendsPageById(String srcOrgId, String targetOrgId, String type,
			Integer extendType) {

		// 保存引用关系
		Where where = Where.create();
		where.eq(DiyPageLibRel::getObjId, targetOrgId);
		DiyPageLibRel rel = dao.queryObject(DiyPageLibRel.class, where);
		if (ObjUtils.notEmpty(rel)) {
			// 存在关系
			rel.setIsRefs(1);
			rel.setPageId(null);
			rel.setExtendsType(extendType);
			rel.setRefsPageId(srcOrgId);
			// 机构关系
			if ("first".equals(type)) {
				SysOrg orgByOrgcode = iser.getOrgByOrgcode(srcOrgId);
				if (ObjUtils.notEmpty(orgByOrgcode)) {
					rel.setRefsPageName(orgByOrgcode.getOrgname());
				}
			}
			// 岗位关系
			if ("second".equals(type)) {
				List<SysPost> postList = comServ.getPostById(srcOrgId);
				if (StringUtils.isNotEmpty(postList)) {
					rel.setRefsPageName(postList.get(0).getName());
				}
			}
			// 人员关系
			if ("third".equals(type)) {
				List<SysEmployeeInfo> employeeList = comServ.getEmployeeById(srcOrgId);
				if (StringUtils.isNotEmpty(employeeList)) {
					rel.setRefsPageName(employeeList.get(0).getEmpname());
				}
			}
			dao.updateById(rel);
		} else {
			// 不存在关系 新增关系
			DiyPageLibRel newRel = new DiyPageLibRel();
			newRel.setId(TMUID.getUID());
			newRel.setType(type);
			newRel.setObjId(targetOrgId);
			newRel.setPageId(null);
			newRel.setExtendsType(extendType);
			newRel.setIsRefs(1);
			newRel.setRefsPageId(srcOrgId);
			// 机构关系
			if ("first".equals(type)) {
				SysOrg orgByOrgcode = iser.getOrgByOrgcode(srcOrgId);
				if (ObjUtils.notEmpty(orgByOrgcode)) {
					newRel.setRefsPageName(orgByOrgcode.getOrgname());
				}
			}
			// 岗位关系
			if ("second".equals(type)) {
				List<SysPost> postList = comServ.getPostById(srcOrgId);
				if (StringUtils.isNotEmpty(postList)) {
					rel.setRefsPageName(postList.get(0).getName());
				}
			}
			// 人员关系
			if ("third".equals(type)) {
				List<SysEmployeeInfo> employeeList = comServ.getEmployeeById(srcOrgId);
				if (StringUtils.isNotEmpty(employeeList)) {
					rel.setRefsPageName(employeeList.get(0).getEmpname());
				}
			}
			dao.insert(newRel);
		}
		return infoService.getPageLibInfoByPageId(srcOrgId, type);
	}

	/**
	 * 上级继承
	 *
	 * @return 返回上级的设置
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public List<DiyPageLibInfoVo> extendsPageBySuper(SysOrgTreeData param, String type, Integer extendType) {
		// 获取全部关系
		List<DiyPageLibRel> relations = dao.queryList(DiyPageLibRel.class);
		// 获取全部引用
//        Map<String, DiyPageLibRel> relationMap = relations.stream()
//                .filter(i -> i.getIsRefs() == 1)
//                .collect(Collectors.toMap(DiyPageLibRel::getObjId, Function.identity()));
//        
		Map<String, DiyPageLibRel> relationMap = relations.stream().filter(i -> i.getIsRefs() == 1)
				.collect(Collectors.toMap(DiyPageLibRel::getObjId, Function.identity(), (v1, v2) -> v2));

		// 获取全部机构映射
		Map<String, DiyPageLibRel> diyPageLibRelMap = relations.stream().filter(i -> i.getIsRefs() == 0)
				.collect(Collectors.toMap(DiyPageLibRel::getObjId, Function.identity(), (v1, v2) -> v2));
		// 通过节点找寻 所有父节点
		String porgStr = param.getOrgpath();
		// 从头到尾
		List<String> porgList = Arrays.asList(porgStr.split("/"));
		String pageId = "";
		// 逆序 找最近父节点
		for (int i = porgList.size() - 2; i >= 0; i--) {
			String porg = porgList.get(i);
			if (StringUtils.isEmpty(porg)) {
				continue;
			}
			if (relationMap.containsKey(porg)) {
				// 保存引用
				this.extendsPageById(relationMap.get(porg).getObjId(), param.getOrgcode(), type, 2);
				pageId = relationMap.get(porg).getObjId();
				break;
			} else if (diyPageLibRelMap.containsKey(porg)) {
				this.extendsPageById(diyPageLibRelMap.get(porg).getObjId(), param.getOrgcode(), type, 2);
				pageId = diyPageLibRelMap.get(porg).getObjId();
				break;
			}
		}
		return infoService.getPageLibInfoByPageId(pageId, type);
	}

	/**
	 * 按照类型获取模板
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public List<DiyPageLibVo> getTemPlateByType(String type) {
		List<DiyPageLibVo> pageLibVos = new ArrayList<>();
		Map<String, String> map = null;
		if ("first".equals(type)) {
			List<SysOrg> orgList = dao.queryList(SysOrg.class);
			map = orgList.stream().collect(Collectors.toMap(SysOrg::getOrgcode, SysOrg::getOrgname));
		}
		if ("second".equals(type)) {
			List<SysPost> postList = dao.queryList(SysPost.class);
			map =
					postList.stream().filter(item-> StringUtils.isNotEmpty(item.getName())).collect(Collectors.toMap(SysPost::getId,
					SysPost::getName));
		}
		if ("third".equals(type)) {
			List<SysEmployeeInfo> userList = dao.queryList(SysEmployeeInfo.class);
			if (StringUtils.isNotEmpty(userList)) {
				map = userList.stream()
						.filter(i -> StringUtils.isNotEmpty(i.getId()) && StringUtils.isNotEmpty(i.getEmpname()))
						.collect(Collectors.toMap(SysEmployeeInfo::getId, SysEmployeeInfo::getEmpname));
			}
			map.put("administrator", "超级管理员");
		}
		// 自定义
		if ("zero".equals(type)) {
			PageParamDto paramDto = new PageParamDto();
			paramDto.setType(type);
			return this.getPage(paramDto, null);
		} else {
			Where where = Where.create();
			where.eq(DiyPageLibRel::getType, type);
			List<DiyPageLibRel> relList = dao.rawQueryListByWhere(DiyPageLibRel.class, where);
			for (DiyPageLibRel diyPageLibRel : relList) {
				DiyPageLibVo beanVo = new DiyPageLibVo();
				// 对于机构来说 对象id 即pageId
				beanVo.setPageId(diyPageLibRel.getObjId());
				beanVo.setTitle(map.get(diyPageLibRel.getObjId()) + "模板");
				pageLibVos.add(beanVo);
			}
			return pageLibVos;
		}
	}

	/**
	 * 根据pageId 修改显示类型
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public void updateShowTypeByPageId(String pageId, Integer showType, Integer showWay, Integer isExportDataSource,
			Integer isUsePersonMenu, String menuSize) {
		Where where = Where.create();
		where.eq(DiyPageLib::getUsed, 1);
		where.in(DiyPageLib::getPageId, pageId.split(","));
		List<DiyPageLib> libs = dao.rawQueryListByWhere(DiyPageLib.class, where);
		for (DiyPageLib lib : libs) {
			if (ObjUtils.notEmpty(lib)) {
				lib.setShowType(showType);
				lib.setShowWay(showWay);
				lib.setIsExportDataSource(isExportDataSource);
				lib.setIsUsePersonMenu(isUsePersonMenu);
				lib.setMenuSize(menuSize);
			}
		}
		if (StringUtils.isNotEmpty(libs)) {
			dao.updateByIdBatch(libs);
		}
	}

	/**
	 * 根据机构id 获取显示类型
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public JSONObject getShowTypeByOrg(String objId, String type) {
		// 根据 对象id找pageId
		String pageId = "";
		if ("zero".equals(type)) {
			pageId = objId;
		} else {
			pageId = infoService.getPageIdByOrg(objId, type);
		}
		if (StringUtils.isEmpty(pageId)) {
			return null;
		}
		// 根据pageid找 lib.id
		Where where = Where.create();
		where.eq(DiyPageLib::getUsed, 1);
		if (pageId.contains(",")) {
			where.in(DiyPageLib::getPageId, pageId.split(","));
		} else {
			where.eq(DiyPageLib::getPageId, pageId);
		}
		List<DiyPageLib> libs = dao.rawQueryListByWhere(DiyPageLib.class, where);
		libs = libs.stream().filter(i -> ObjUtils.notEmpty(i.getShowType()) || ObjUtils.notEmpty(i.getIsUsePersonMenu())
				|| ObjUtils.notEmpty(i.getMenuSize())).collect(Collectors.toList());
		if (StringUtils.isEmpty(libs)) {
			return null;
		}
		JSONObject obj = new JSONObject();
		obj.put("showType", libs.get(libs.size() - 1).getShowType());
		obj.put("showWay", libs.get(libs.size() - 1).getShowWay());
		obj.put("isUsePersonMenu", libs.get(libs.size() - 1).getIsUsePersonMenu());
		obj.put("isExportDataSource", libs.get(libs.size() - 1).getIsExportDataSource());
		obj.put("menuSize", libs.get(libs.size() - 1).getMenuSize());
		return obj;
	}

	/**
	 * 根据机构id 获取继承方式
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public JSONObject getExtendsByOrg(String objId, String type) {
		Where where = Where.create();
		where.eq(DiyPageLibRel::getObjId, objId);
		where.eq(DiyPageLibRel::getType, type);
		DiyPageLibRel rel = dao.queryObject(DiyPageLibRel.class, where);
		if (ObjUtils.isEmpty(rel)) {
			return null;
		}
		JSONObject obj = new JSONObject();
		obj.put("extendType", rel.getExtendsType());
		obj.put("extendsOrgName", rel.getRefsPageName());
		return obj;
	}

	/**
	 * 根据机构id 获取来源方式 引用还是复制
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public Integer getOrignType(String objId, String type) {
		Where where = Where.create();
		where.eq(DiyPageLibRel::getObjId, objId);
		where.eq(DiyPageLibRel::getType, type);
		DiyPageLibRel rel = dao.queryObject(DiyPageLibRel.class, where);
		if (ObjUtils.isEmpty(rel)) {
			return null;
		}
		return rel.getIsRefs();
	}

	/**
	 * 根据当前登录用户获取 机构首页 岗位首页 个人首页
	 *
	 * @return
	 * <AUTHOR>
	 * @params
	 */
	@Override
	public JSONObject getCurrentUserPages() {
		SysUser currentUser = SysUserUtil.getCurrentUser();
		String orgcode = currentUser.getOrgId();
		String id = currentUser.getId();
		String postId = currentUser.getPostId();
		JSONObject obj = new JSONObject();
		obj.put("orgPage", null);
		obj.put("postPage", null);
		obj.put("personPage", null);
		// 获取页面
		if (StringUtils.isNotEmpty(orgcode)) {
			// 机构首页
			// 重定向
			String pageId = orgcode;
			String type = "first";
			JSONObject redirectPage = this.redirectPage(pageId, type);
			if (StringUtils.isNotEmpty(redirectPage)) {
				pageId = redirectPage.getString("pageId");
				type = redirectPage.getString("type");
			}
			List<DiyPageLibInfoVo> first = infoService.getPageLibInfoByPageId(pageId, type);
			if (StringUtils.isNotEmpty(first)) {
				obj.put("orgPageId", pageId);
				obj.put("orgPageType", type);
				obj.put("orgPage", first);
			}
		}
		if (StringUtils.isNotEmpty(postId)) {
			// 岗位首页
			// 重定向
			String pageId = postId;
			String type = "second";
			JSONObject redirectPage = this.redirectPage(pageId, type);
			if (StringUtils.isNotEmpty(redirectPage)) {
				pageId = redirectPage.getString("pageId");
				type = redirectPage.getString("type");
			}
			List<DiyPageLibInfoVo> second = infoService.getPageLibInfoByPageId(pageId, type);
			if (StringUtils.isNotEmpty(second)) {
				obj.put("postPageId", pageId);
				obj.put("postPageType", type);
				obj.put("postPage", second);
			}
		}
		if (StringUtils.isNotEmpty(id)) {
			// 个人首页
			// 重定向
			String pageId = id;
			String type = "third";
			JSONObject redirectPage = this.redirectPage(pageId, type);
			if (StringUtils.isNotEmpty(redirectPage)) {
				pageId = redirectPage.getString("pageId");
				type = redirectPage.getString("type");
			}
			List<DiyPageLibInfoVo> third = infoService.getPageLibInfoByPageId(pageId, type);
			if (StringUtils.isNotEmpty(third)) {
				obj.put("personPageId", pageId);
				obj.put("personPageType", type);
				obj.put("personPage", third);
			}
		}
		return obj;
	}

	@Override
	public void exportTds(@RequestBody List<TdsExportDto> params) {
		Workbook workbook = null;
		for (TdsExportDto param : params) {
			String title = null;// 数据源名称
			String subTitle = null; // 副标题
			String sheetName = param.getSheetTitle();
			if (param.getIsLoadPrinter() != null && param.getIsLoadPrinter()) {// 导出excel读打印配置信息
				String moduleCode = "system.tds";
				String code = param.getTdsAlias();

				// 打印报表信息初始化
				Map<String, Object> reportConfig = param.getReportConfig();
				if (reportConfig != null) {
					String mc = (String) reportConfig.get("moduleCode"); // 报表模块编码
					String ri = (String) reportConfig.get("reportId"); // 报表id
					if (StringUtils.isNotEmpty(ri)) {
						moduleCode = mc;
						code = ri;
					}
				}

				param.setPrinterConfig(printerServ.getPrinterConfig(moduleCode, code));
				param.setPrinterColumn(printerServ.getPrinterColList(moduleCode, code));
				if (StringUtils.isEmpty(param.getPrinterConfig())) {
					param.setPrinterConfig(null);
				}
				if (StringUtils.isEmpty(param.getPrinterColumn())) {
					param.setPrinterColumn(null);
				}
			}
			try {
				if (StringUtils.isNotEmpty(param.getExportData())) {// 根据数据和配置进行导出
					ExcelExport.exportExcel(param.getExportData(), param.getPrinterConfig(), param.getPrinterColumn(),
							response);
				} else {// 数据源导出
					JSONArray inpara = new JSONArray();
					if (StringUtils.isNotEmpty(param.getInParaAlias())) {
						inpara = JSONArray.parseArray(param.getInParaAlias());
					}
					IDataSource tds = tdsServ.getTdsDataList(param.getTdsAlias(), inpara, true);
					workbook = ExcelExport.tdsExportExcel(param.getQueryData(), tds.getJsonWithoutData(),
							tds.getDataList(), param.getPrinterConfig(), param.getPrinterColumn(), param.getTempType(),
							response, sheetName, false, workbook);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		ExcelExport.downLoadExcel("自定义", response, workbook);// 使用response将workbook内容写回浏览器
	}

}
