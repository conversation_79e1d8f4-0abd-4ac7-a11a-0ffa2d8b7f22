package com.yunhesoft.system.tools.database.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.database.service.SysDataBaseService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/system/tools/database")
@Api(tags = "数据库信息")
public class SysDataBaseController extends BaseRestController {
	@Autowired
	private SysDataBaseService dbServ;

	@ApiOperation(value = "获取数据库表信息", notes = "获取数据库表信息")
	@RequestMapping(value = "/getTable", method = { RequestMethod.GET })
	public Res<?> getTable(@RequestParam String tableName) {
		return Res.OK(dbServ.getTable(tableName));
	}

	@ApiOperation(value = "获取表字段信息", notes = "获取表字段信息")
	@RequestMapping(value = "/getColumns", method = { RequestMethod.GET })
	public Res<?> getTableColumns(@RequestParam String tableName) {
		return Res.OK(dbServ.getColumns(tableName));
	}

	@ApiOperation(value = "获取数据库表列表", notes = "获取数据库表列表信息")
	@RequestMapping(value = "/getTables", method = { RequestMethod.GET })
	public Res<?> getTable() {
		return Res.OK(dbServ.getTables());
	}

}
