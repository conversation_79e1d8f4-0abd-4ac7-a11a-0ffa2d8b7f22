package com.yunhesoft.joblist.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.quartz.CronExpression;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.StringUtils;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.joblist.entity.dto.JobFrequencyCalcDto;
import com.yunhesoft.joblist.entity.dto.StandardJobLibQueryDto;
import com.yunhesoft.joblist.entity.po.CycleScheme;
import com.yunhesoft.joblist.entity.vo.JobFrequencyCalcVo;
import com.yunhesoft.joblist.service.IJobFrequencyCalcService;
import com.yunhesoft.joblist.service.IJoblistMethodService;
import com.yunhesoft.leanCosting.calcLogic.PublicMethods;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class JobFrequencyCalcServiceImpl implements IJobFrequencyCalcService {

	@Autowired
	private IJoblistMethodService ijms;

	@Override
	public List<JobFrequencyCalcVo> getCycleSchemeFrequency(JobFrequencyCalcDto dto) {
		List<JobFrequencyCalcVo> rtn = new ArrayList<JobFrequencyCalcVo>();
		StandardJobLibQueryDto cdto = new StandardJobLibQueryDto();
		cdto.setId(dto.getCycleSchemeID());
		List<CycleScheme> ccl = this.ijms.getCycleSchemeList(cdto);
		if (ccl != null && ccl.size() > 0) {
			CycleScheme cs = ccl.get(0);
			Integer iscron = cs.getIsCron();
			if (iscron == null) {
				iscron = 0;
			}
			String config = cs.getCycleStr();
			if (iscron == 1) {
				// cron表达式
				rtn.addAll(this.getFrequencyFromCron(config, dto.getBeginTime(), dto.getEndTime()));
			} else {
				// 不是cron表达式
				Integer ctype = cs.getBindCycle();// 1、班；2、日；3、周；4、月；5、季；6、年；
				rtn.addAll(this.getFrequencyFromJson(config, dto.getWorkDate(), dto.getBeginTime(), dto.getEndTime(),
						ctype));
			}
		}
		return rtn;
	}

	/**
	 * @category 从cron表达式获取频次
	 * @param cronExpress
	 * @param kssj
	 * @param jzsj
	 * @return
	 */
	private List<JobFrequencyCalcVo> getFrequencyFromCron(String cronExpress, String kssj, String jzsj) {
		List<JobFrequencyCalcVo> rtn = new ArrayList<JobFrequencyCalcVo>();
		if (StringUtils.isEmpty(cronExpress)) {
			log.error("cron解析", "cron表达式不能为空");
		} else {
			try {
				CronExpression ce = new CronExpression(cronExpress);
				Date st = DateTimeUtils.doSecond(DateTimeUtils.parseDateTime(kssj),-1);
				Date et = DateTimeUtils.parseDateTime(jzsj);
				Date nt = ce.getNextValidTimeAfter(st);// 开始时间后第一次的执行时间
				while (DateTimeUtils.bjDate(et, nt) > 0) {
					// 执行时间在下班时间以前，不包含下班时间（下班时间算到下一班次）
					rtn.add(this.setVo(DateTimeUtils.formatDateTime(nt), kssj, jzsj, false));
					nt = ce.getNextValidTimeAfter(nt);// nt时间后下一次的执行时间
				}
			} catch (ParseException e) {
				log.error("cron解析", "cron表达式不正确,传入的cron表达式" + cronExpress);
			}
		}
		return rtn;
	}

	private JobFrequencyCalcVo setVo(String nt, String kssj, String jzsj, boolean iseach) {
		JobFrequencyCalcVo vo = new JobFrequencyCalcVo();
		vo.setFrequencyNo(nt);
		vo.setBeginTime(kssj);
		vo.setEndTime(jzsj);
		vo.setEachTeam(iseach);
		return vo;
	}

	/**
	 * @category 从json获取频次
	 * @param jsonExpress//周期设置
	 * @param rq//当班日期
	 * @param kssj//上班时间
	 * @param jzsj//下班时间
	 * @param zqlx//            1、班；2、日；3、周；4、月；5、季；6、年；
	 * @return
	 */
	private List<JobFrequencyCalcVo> getFrequencyFromJson(String jsonExpress, String rq, String kssj, String jzsj,
			Integer zqlx) {
		List<JobFrequencyCalcVo> rtn = new ArrayList<JobFrequencyCalcVo>();
		if (StringUtils.isEmpty(jsonExpress)) {
			log.error("周期解析", "周期表达式不能为空");
		} else {
			try {
				JSONObject json = JSONObject.parseObject(jsonExpress);
				PublicMethods pm = new PublicMethods();
				Date st = DateTimeUtils.parseDateTime(kssj);
				Date et = DateTimeUtils.parseDateTime(jzsj);
				String flx;
				Integer sel;
				Integer interval = pm.convertInteger(json.getString("interval"), 1);// 周期间隔暂时只能是1，其它的需要再考虑
				String frequency = json.getString("frequency");// 频次
				if (zqlx == 1) {
					// 班次
					JSONArray jsonArr = JSONArray.parseArray(frequency);
					if (jsonArr != null && jsonArr.size() > 0) {
						int count = jsonArr.size();
						for (int j = 0; count > j; j++) {
							JSONObject row = JSONObject.parseObject(jsonArr.getString(j));
							sel = pm.convertInteger(row.getString("selected"), 0);
							if (sel == 1) {
								// 只能一个被选中
								flx = row.getString("ftype");
								if ("iscount".equals(flx)) {
									// 给定次数
									Integer cs = pm.convertInteger(row.getString("count"), 0);
									for (int yn = 1; cs >= yn; yn++) {
										rtn.add(this.setVo(String.valueOf(yn), kssj, jzsj, false));
									}
								} else if ("isrelative".equals(flx)) {
									// 相对时间
									String rdata = row.getString("rdata");// 这个是数组
									JSONArray array = JSONArray.parseArray(rdata);
									if (array != null && array.size() > 0) {
										int y = array.size();
										HashMap<String, String> tm = new HashMap<String, String>();
										for (int x = 0; y > x; x++) {
											JSONObject r = JSONObject.parseObject(array.getString(x));
											Integer s = pm.convertInteger(r.getString("selected"), 0);
											if (s == 1) {
												String rlx = r.getString("lx");
												Integer sj = pm.convertInteger(r.getString("time"), 0);
												String unit = r.getString("unit");
												if (unit == null) {
													unit = "min";
												}
												Date bzsj = null;
												String xdsj = null;
												if ("sbq".equals(rlx)) {
													bzsj = st;
													sj = 0 - sj;
												} else if ("sbh".equals(rlx)) {
													bzsj = st;
												} else if ("xbq".equals(rlx)) {
													bzsj = et;
													sj = 0 - sj;
												} else if ("xbh".equals(rlx)) {
													bzsj = et;
												}
												if ("sec".equals(unit)) {
													xdsj = DateTimeUtils
															.formatDateTime(DateTimeUtils.doSecond(bzsj, sj));
												} else if ("hour".equals(unit)) {
													xdsj = DateTimeUtils
															.formatDateTime(DateTimeUtils.doHour(bzsj, sj));
												} else {
													xdsj = DateTimeUtils
															.formatDateTime(DateTimeUtils.doMinute(bzsj, sj));
												}
												if (!tm.containsKey(xdsj)) {
													// 避免填入重复的时间
													rtn.add(this.setVo(xdsj, kssj, jzsj, false));
												}
											}
										}
									}
								} else if ("isafter".equals(flx)) {
									// 上班之后
									Integer atime = pm.convertInteger(row.getString("atime"), 0);// 上班后多长时间
									String aunit = row.getString("aunit");// 时间单位
									if (aunit == null) {
										aunit = "min";
									}
									Integer etime = pm.convertInteger(row.getString("etime"), 30);// 间隔多久，默认是30分钟
									String eunit = row.getString("eunit");// 间隔单位
									if (eunit == null) {
										eunit = "min";
									}
									Date xdsj = null;
									if ("sec".equals(aunit)) {
										xdsj = DateTimeUtils.doSecond(st, atime);
									} else if ("hour".equals(aunit)) {
										xdsj = DateTimeUtils.doHour(st, atime);
									} else {
										xdsj = DateTimeUtils.doMinute(st, atime);
									}
									while (DateTimeUtils.bjDate(et, xdsj) > 0) {
										// 执行时间在下班时间以前，不包含下班时间（下班时间算到下一班次）
										rtn.add(this.setVo(DateTimeUtils.formatDateTime(xdsj), kssj, jzsj, false));
										if ("sec".equals(eunit)) {
											xdsj = DateTimeUtils.doSecond(xdsj, etime);
										} else if ("hour".equals(eunit)) {
											xdsj = DateTimeUtils.doHour(xdsj, etime);
										} else {
											xdsj = DateTimeUtils.doMinute(xdsj, etime);
										}
									}
								}
								break;
							}
						}
					}
				} else if (zqlx == 2) {
					// 日,暂时仅是几次
					Integer iseveryteam = pm.convertInteger(json.getString("iseveryteam"), 1);// 每个班
					boolean iseach = false;
					if (iseveryteam == 1) {
						iseach = true;
					}
					JSONArray jsonArr = JSONArray.parseArray(frequency);
					if (jsonArr != null && jsonArr.size() > 0) {
						int count = jsonArr.size();
						for (int j = 0; count > j; j++) {
							JSONObject row = JSONObject.parseObject(jsonArr.getString(j));
							sel = pm.convertInteger(row.getString("selected"), 0);
							if (sel == 1) {
								// 只能一个被选中
								flx = row.getString("ftype");
								if ("iscount".equals(flx)) {
									// 给定次数
									Integer cs = pm.convertInteger(row.getString("count"), 0);
									for (int yn = 1; cs >= yn; yn++) {
										rtn.add(this.setVo(String.valueOf(yn), rq, rq, iseach));
									}
								}
								break;
							}
						}
					}
				} else if (zqlx == 3) {
					// 周,暂时仅是几次
					String ksrq, jzrq;
					// 传入日期是周几
					String[] jd = DateTimeUtils.getWeek(DateTimeUtils.parseDate(rq));// 推导日期对应的时间点
					ksrq = jd[0].substring(0, 10);
					jzrq = jd[1].substring(0, 10);
					Integer iseveryteam = pm.convertInteger(json.getString("iseveryteam"), 1);// 每个班
					boolean iseach = false;
					if (iseveryteam == 1) {
						iseach = true;
					}
					JSONArray jsonArr = JSONArray.parseArray(frequency);
					if (jsonArr != null && jsonArr.size() > 0) {
						int count = jsonArr.size();
						for (int j = 0; count > j; j++) {
							JSONObject row = JSONObject.parseObject(jsonArr.getString(j));
							sel = pm.convertInteger(row.getString("selected"), 0);
							if (sel == 1) {
								// 只能一个被选中
								flx = row.getString("ftype");
								if ("iscount".equals(flx)) {
									// 给定次数
									Integer cs = pm.convertInteger(row.getString("count"), 0);
									for (int yn = 1; cs >= yn; yn++) {
										rtn.add(this.setVo(String.valueOf(yn), ksrq, jzrq, iseach));
									}
								}
								break;
							}
						}
					}
				} else if (zqlx == 4) {
					// 月,暂时仅是几次
					String ksrq, jzrq, yf;
					yf = rq.substring(0, 7);
					ksrq = yf + "-01";
					jzrq = DateTimeUtils.getMonthEnd(yf);
					Integer iseveryteam = pm.convertInteger(json.getString("iseveryteam"), 1);// 每个班
					boolean iseach = false;
					if (iseveryteam == 1) {
						iseach = true;
					}
					JSONArray jsonArr = JSONArray.parseArray(frequency);
					if (jsonArr != null && jsonArr.size() > 0) {
						int count = jsonArr.size();
						for (int j = 0; count > j; j++) {
							JSONObject row = JSONObject.parseObject(jsonArr.getString(j));
							sel = pm.convertInteger(row.getString("selected"), 0);
							if (sel == 1) {
								// 只能一个被选中
								flx = row.getString("ftype");
								if ("iscount".equals(flx)) {
									// 给定次数
									Integer cs = pm.convertInteger(row.getString("count"), 0);
									for (int yn = 1; cs >= yn; yn++) {
										rtn.add(this.setVo(String.valueOf(yn), ksrq, jzrq, iseach));
									}
								}
								break;
							}
						}
					}
				} else if (zqlx == 5) {
					// 季,暂时仅是几次
					String ksrq, jzrq, ys, nf;
					nf = rq.substring(0, 4);
					ys = rq.substring(5, 7);
					if ("01".equals(ys) || "02".equals(ys) || "03".equals(ys)) {
						ksrq = nf + "-01-01";
						jzrq = nf + "-03-31";
					} else if ("04".equals(ys) || "05".equals(ys) || "06".equals(ys)) {
						ksrq = nf + "-04-01";
						jzrq = nf + "-06-30";
					} else if ("07".equals(ys) || "08".equals(ys) || "09".equals(ys)) {
						ksrq = nf + "-07-01";
						jzrq = nf + "-09-30";
					} else {
						ksrq = nf + "-10-01";
						jzrq = nf + "-12-31";
					}
					Integer iseveryteam = pm.convertInteger(json.getString("iseveryteam"), 1);// 每个班
					boolean iseach = false;
					if (iseveryteam == 1) {
						iseach = true;
					}
					JSONArray jsonArr = JSONArray.parseArray(frequency);
					if (jsonArr != null && jsonArr.size() > 0) {
						int count = jsonArr.size();
						for (int j = 0; count > j; j++) {
							JSONObject row = JSONObject.parseObject(jsonArr.getString(j));
							sel = pm.convertInteger(row.getString("selected"), 0);
							if (sel == 1) {
								// 只能一个被选中
								flx = row.getString("ftype");
								if ("iscount".equals(flx)) {
									// 给定次数
									Integer cs = pm.convertInteger(row.getString("count"), 0);
									for (int yn = 1; cs >= yn; yn++) {
										rtn.add(this.setVo(String.valueOf(yn), ksrq, jzrq, iseach));
									}
								}
								break;
							}
						}
					}
				} else if (zqlx == 6) {
					// 年,暂时仅是几次
					String ksrq, jzrq, nf;
					nf = rq.substring(0, 4);
					ksrq = nf + "-01-01";
					jzrq = nf + "-12-31";
					Integer iseveryteam = pm.convertInteger(json.getString("iseveryteam"), 1);// 每个班
					boolean iseach = false;
					if (iseveryteam == 1) {
						iseach = true;
					}
					JSONArray jsonArr = JSONArray.parseArray(frequency);
					if (jsonArr != null && jsonArr.size() > 0) {
						int count = jsonArr.size();
						for (int j = 0; count > j; j++) {
							JSONObject row = JSONObject.parseObject(jsonArr.getString(j));
							sel = pm.convertInteger(row.getString("selected"), 0);
							if (sel == 1) {
								// 只能一个被选中
								flx = row.getString("ftype");
								if ("iscount".equals(flx)) {
									// 给定次数
									Integer cs = pm.convertInteger(row.getString("count"), 0);
									for (int yn = 1; cs >= yn; yn++) {
										rtn.add(this.setVo(String.valueOf(yn), ksrq, jzrq, iseach));
									}
								}
								break;
							}
						}
					}
				}
			} catch (Exception e) {
				log.error("周期解析", "周期表达式不正确,传入的表达式" + jsonExpress);
			}
		}
		return rtn;
	}
}
