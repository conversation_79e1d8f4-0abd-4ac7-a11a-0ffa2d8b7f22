package com.yunhesoft.mobile.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.mobile.entity.dto.DeviceStatusSwitchDto;
import com.yunhesoft.mobile.entity.dto.MobLeanCostingDto;
import com.yunhesoft.mobile.service.IMobLeanCostingService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/mobile/leanCosting")
@Api(tags = "移动端 - 精益核算")
public class MobLeanCostingController extends BaseRestController {
    @Autowired
    private IMobLeanCostingService mobLeanCostingService;

    @ApiOperation("移动端 - 精益核算 - 获取核算对象列表")
    @RequestMapping(value = "/getAcctObjs", method = {RequestMethod.POST})
    public Res getAccountingObjectList(@RequestBody MobLeanCostingDto param) {
        return Res.OK(mobLeanCostingService.getAccountingObjectList(param));
    }

    @ApiOperation("移动端 - 精益核算 - 获取核算对象录入状态列表")
    @RequestMapping(value = "/getAcctObjsInputed", method = {RequestMethod.POST})
    public Res getAccountingObjectInputedStatus(@RequestBody MobLeanCostingDto param) {
        return Res.OK(mobLeanCostingService.getAccountingObjectInputedStatus(param));
    }

    @ApiOperation("移动端 - 精益核算 - 获取核算对象采集点")
    @RequestMapping(value = "/getAcctObjCollectionPoints", method = {RequestMethod.POST})
    public Res getAccountingObjectCollectionPointList(@RequestBody MobLeanCostingDto param) {
        return Res.OK(mobLeanCostingService.getAccountingObjectCollectionPointList(param));
    }

    @ApiOperation("移动端 - 精益核算 - 获取当前登录人员信息")
    @RequestMapping(value = "/getCurUserInfo", method = {RequestMethod.POST})
    public Res getCurUserInfo(@RequestBody MobLeanCostingDto param) {
        return Res.OK(mobLeanCostingService.getCurUserInfo(param));
    }

    @ApiOperation("移动端 - 精益核算 - 获取班次列表")
    @RequestMapping(value = "/getShiftData", method = {RequestMethod.POST})
    public Res getShiftData(@RequestBody MobLeanCostingDto param) {
        return Res.OK(mobLeanCostingService.getShiftData(param));
    }

    @ApiOperation("移动端 - 精益核算 - 保存录入数据")
    @RequestMapping(value = "/saveInputData", method = {RequestMethod.POST})
    public Res saveInputData(@RequestBody MobLeanCostingDto param) {
        try {
            return Res.OK(mobLeanCostingService.saveInputData(param));
        } catch (Exception e) {
            return Res.FAIL(e.getMessage());
        }
    }

    @ApiOperation("移动端 - 精益核算 - 获取录入数据")
    @RequestMapping(value = "/getInputData", method = {RequestMethod.POST})
    public Res getInputData(@RequestBody MobLeanCostingDto param) {
        try {
            return Res.OK(mobLeanCostingService.getInputData(param));
        } catch (Exception e) {
            return Res.FAIL(e.getMessage());
        }
    }

    @ApiOperation("移动端 - 精益核算 - 获取核算对象分类树数据")
    @RequestMapping(value = "/getAcctClassTree", method = {RequestMethod.POST})
    public Res getAcctClassTree(@RequestBody MobLeanCostingDto param) {
        try {
            return Res.OK(mobLeanCostingService.getAcctClassTree(param));
        } catch (Exception e) {
            return Res.FAIL(e.getMessage());
        }
    }

    @ApiOperation("移动端 - 精益核算 - 获取设备状态切换数据")
    @RequestMapping(value = "/getDeviceStatusSwitchData", method = {RequestMethod.POST})
    public Res getDeviceStatusSwitchData(@RequestBody DeviceStatusSwitchDto param) {
        try {
            return Res.OK(mobLeanCostingService.getDeviceStatusSwitchData(param));
        } catch (Exception e) {
            return Res.FAIL(e.getMessage());
        }
    }

    @ApiOperation("移动端 - 精益核算 - 图片识别")
    @RequestMapping(value = "/imageOcr", method = {RequestMethod.POST})
    public Res imageOcr(@RequestParam("file") MultipartFile image, @RequestParam("ocrType") String ocrType) {
        try {
            return Res.OK(mobLeanCostingService.imageOcr(image, ocrType));
        } catch (Exception e) {
            return Res.FAIL(e.getMessage());
        }
    }

    @ApiOperation("移动端 - 精益核算 - 查询缩略图列表")
    @RequestMapping(value = "/getAccountingQueryImgList", method = {RequestMethod.POST})
    public Res getAccountingQueryImgList(@RequestBody MobLeanCostingDto param) {
        try {
            return Res.OK(mobLeanCostingService.getAccountingQueryImgList(param));
        } catch (Exception e) {
            return Res.FAIL(e.getMessage());
        }
    }

    @ApiOperation("移动端 - 精益核算 - 获取按时录入的时间列表")
    @RequestMapping(value = "/getOnTimeData", method = {RequestMethod.POST})
    public Res getOnTimeData(@RequestBody MobLeanCostingDto param) {
        return Res.OK(mobLeanCostingService.getOnTimeData(param));
    }
}
