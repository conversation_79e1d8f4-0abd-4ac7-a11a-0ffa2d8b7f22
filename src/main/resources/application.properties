spring.application.name=TM4-SYSTEM
#项目信息配置
app.project=TM4-SYSTEM
app.application=TM4-SYSTEM
app.description=TM4核心框架
app.module=tm4-system

#系统环境运行模式 dev：开发；test：测试
app.mode=dev

#证书配置文件路径
#app.certificate.propfile=D:\\MyWorking\\tmca\\ca.properties

#是否启用调度自动发送微信消息
app.auto.sendmsg=false
#是否保存日志到关系数据库(不推荐，需要配置log4j2.xml文件)
app.savelog2db=false

#tm3.5系统地址
app.Tm3Url=http://127.0.0.1:8080/tm3

#TM3.5地址，需要做同域，便于TM4嵌套
app.outsys.clientSecret=http://127.0.0.1:8080/tm3
#tomcat
server.port=8889
server.servlet.context-path=/tm4main

#启用哪些配置文件
#** version,config必须激活
#** 根据项目情况启用 mysql or sqlserver or tm3
#spring.profiles.active=version,config,sqlserver,oracle,postgresql,kingbase
spring.profiles.active=version,config,kingbase


#kingbase 人大金仓，*使用Activiti工作流必须配置 productName=PostgreSQL

#spring.datasource.url=jdbc:kingbase8://************:54321/yhsoft?currentSchema=tlm,sys_catalog
#spring.datasource.username=yunhe
#spring.datasource.password=bzhs!*6

#TEST
#spring.datasource.url=jdbc:kingbase8://************:54321/tm4dsz?currentSchema=tm4dsz,sys_catalog
#spring.datasource.username=tm4dsz
#spring.datasource.password=YHbzhs!*6

#guangdong
spring.datasource.url=jdbc:kingbase8://************:54321/gd_bzjygl?currentSchema=gd_bzjygl,sys_catalog
spring.datasource.username=gd_bzjygl
spring.datasource.password=YHbzhs!*6

#kingbase 人大金仓 辽化
#spring.datasource.url=jdbc:kingbase8://************:54321/lh_bzjygl?currentSchema=lh_bzjygl,sys_catalog
#spring.datasource.username=lh_bzjygl
#spring.datasource.password=YHbzhs!*6


#数据库类型，必须配置
#spring.activiti.database-type=postgres
#第一次执行配置为true，自动建表，之后执行改为none
#spring.activiti.database-schema-update=none


#mysql数据库连接
#spring.datasource.url=****************************************************************************************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=YHbzhs!*6


#sqlserver数据库
#spring.datasource.url=***************************************************************************
#spring.datasource.username=sa
#spring.datasource.password=bzhs!*6

##oracle##
#spring.datasource.url=****************************************
#spring.datasource.username=DEV_GDSH
#spring.datasource.password=DEV_GDSH123


#postgresql数据库
#spring.datasource.url=****************************************
#spring.datasource.username=postgres
#spring.datasource.password=bzhs!*6

#达梦数据库连接
#spring.datasource.url=jdbc:dm://***************:5236
#spring.datasource.username=TM4WEBD
#spring.datasource.password=TM4@ZBg30XsmnRmrWZ9IslFs2ckl_Vb7L3bZTcPcsL8tFToDAvz188utXJtSj4lx7uYUy7W8kpgV0efvjrsUrdVwRpYGR4wHpx8YikKN08j8GrsAAd1xHC6ub-RNdc3J59ebfgs4C3esOhkaQr33WsTN_RrGAOw98SGlPIEQxvLTmx4






#redis
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.database=2
spring.redis.password=TM4@ttm5CBFMf8n3VvihZvFwdBV0uy55iA2xPsNXGqx3Dx9e6u0IgNEdrhD376PNkGDeJtl7befexPUB_EXXOiaKYj5D_FbeCPoMrrXa_0QpMaBfQZws6qqXpEnPNKrC08OT4XXvxZBj_yaFnWfPm1V0w-PmPBsNy0I8I-TGq9c2fWk
#spring.redis.password=ENC@YmR5aA==


#redis 支持https
#spring.redis.ssl=true
#spring.redis.host=************
#spring.redis.port=31680
#spring.redis.database=2
#spring.redis.password=YHbzhs!*6


#tm3数据库连接
#** 根据项目情况是否启用，依赖配置文件tm3
#spring.datasource.tm3.url=*****************************************************************************************
#spring.datasource.tm3.username=sa
#spring.datasource.tm3.password=TM4@ZBg30XsmnRmrWZ9IslFs2ckl_Vb7L3bZTcPcsL8tFToDAvz188utXJtSj4lx7uYUy7W8kpgV0efvjrsUrdVwRpYGR4wHpx8YikKN08j8GrsAAd1xHC6ub-RNdc3J59ebfgs4C3esOhkaQr33WsTN_RrGAOw98SGlPIEQxvLTmx4

#springboot 去掉自动mongodb驱动，解决启动报错问题，如启用mongodb请注释本条配置
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration
#mongodb数据库连接
#logging.config=classpath:log4j2mongo.xml
#spring.data.mongodb.host=127.0.0.1
#spring.data.mongodb.port=27017
#spring.data.mongodb.database=tm4
#spring.data.mongodb.auto-index-creation=true
#spring.data.mongodb.username=
#spring.data.mongodb.password=


#文件上传模式：此处需匹配ISysFileService实现类的ConditionalOnProperty注解中的生效值
#默认local模式
#local：本地文件系统，minio：文件服务器（minio）,gofast:go-fastDfs

#file.upload.mode=local

file.upload.mode=gofast
file.gofast.url=http://************:8080
file.gofast.group=group1

#公共文件夹名称
#file.upload.publicDirName=public
#minio服务地址，key及密钥  桶名
#minio.url=http://************:9000
#minio.accessKey=vMV4PfKFDRQ4wMl0
#minio.secretKey=MCzw9KbrDbFDzPnWBvz80RAS9SrRv8Bk
#minio.bucketName=tm4-qsmzq
TM4Sys.themesCss = "dsz"

eureka.client.enabled=true
eureka.instance.instanceId=TM4-SYSTEM-1
eureka.instance.prefer-ip-address=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
#eureka.client.service-url.defaultZone=http://************:30552/eureka
eureka.client.service-url.defaultZone=http://localhost:8761/eureka





