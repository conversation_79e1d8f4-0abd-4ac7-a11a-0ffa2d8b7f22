-- PostgreSQL 存储过程：获取上个班次的采集点数据
-- 参数：
-- p_orgcode: 登录人机构代码（班次代码）
-- p_progid: 班组代码
-- p_where_unit: 额外的核算对象id过滤条件（可选）

CREATE OR REPLACE FUNCTION get_previous_shift_collect_data(
    p_orgcode VARCHAR(200),
    p_progid VARCHAR(50),
    p_where_unit TEXT DEFAULT NULL
)
RETURNS TABLE (
    collect_point_id VARCHAR,
    ipt_id VARCHAR,
    collect_point_text VARCHAR,
    collect_point_val VARCHAR,
    input_comp_type VARCHAR,
    input_options VARCHAR,
    input_time VARCHAR,
    job_input_time VARCHAR,
    shift_class_id VARCHAR,
    shift_class_name VARCHAR,
    shift_start_time VARCHAR,
    shift_end_time VARCHAR,
    shift_date VARCHAR
) AS $$
DECLARE
    current_shift_record RECORD;
    previous_shift_record RECORD;
    sql_query TEXT;
BEGIN
    -- 第一步：获取当前班次信息
    SELECT
        orgcode, modelid, dbrq, objid, objname, objtype,
        shiftclassid, shiftclassname, sbsj, xbsj, tjrq, gzsj, progid
    INTO current_shift_record
    FROM shift_data
    WHERE orgcode = p_orgcode
      AND progid = p_progid
      AND dbrq = TO_CHAR(CURRENT_DATE, 'YYYY-MM-DD')
      AND TO_CHAR(CURRENT_TIME, 'HH24:MI:SS') BETWEEN sbsj AND xbsj
    LIMIT 1;

    -- 如果没有找到当前班次，返回空结果
    IF current_shift_record IS NULL THEN
        RAISE NOTICE '未找到当前班次: orgcode=%, progid=%', p_orgcode, p_progid;
        RETURN;
    END IF;

    -- 第二步：获取上个班次信息
    SELECT
        orgcode, modelid, dbrq, objid, objname, objtype,
        shiftclassid, shiftclassname, sbsj, xbsj, tjrq, gzsj, progid
    INTO previous_shift_record
    FROM shift_data
    WHERE orgcode = p_orgcode
      AND progid = p_progid
      AND (
          -- 同一天的前一个班次（基于上班时间）
          (dbrq = current_shift_record.dbrq AND sbsj < current_shift_record.sbsj)
          OR
          -- 前一天的班次（如果当前是第一个班次）
          (dbrq = TO_CHAR(TO_DATE(current_shift_record.dbrq, 'YYYY-MM-DD') - INTERVAL '1 day', 'YYYY-MM-DD'))
      )
    ORDER BY dbrq DESC, sbsj DESC
    LIMIT 1;

    -- 如果没有找到上个班次，返回空结果
    IF previous_shift_record IS NULL THEN
        RAISE NOTICE '未找到上个班次: orgcode=%, progid=%', p_orgcode, p_progid;
        RETURN;
    END IF;

    -- 第三步：构建查询采集点数据的SQL
    sql_query := 'SELECT
        mx.collect_point_id::VARCHAR,
        mx.ipt_id::VARCHAR,
        mx.collect_point_text::VARCHAR,
        mx.collect_point_val::VARCHAR,
        mx.input_comp_type::VARCHAR,
        mx.input_options::VARCHAR,
        mx.input_time::VARCHAR,
        mx.job_input_time::VARCHAR,
        $1::VARCHAR as shift_class_id,
        $2::VARCHAR as shift_class_name,
        $3::VARCHAR as shift_start_time,
        $4::VARCHAR as shift_end_time,
        $5::VARCHAR as shift_date
    FROM acctobj_inputmx mx
    WHERE mx.tmused = 1
      AND EXISTS (
          SELECT ai.id
          FROM acctobj_input ai
          WHERE ai.tmused = 1 ';

    -- 根据是否有额外的单位条件来构建不同的SQL
    IF p_where_unit IS NOT NULL AND LENGTH(TRIM(p_where_unit)) > 0 THEN
        sql_query := sql_query ||
            'AND ai.acctobj_id ' || p_where_unit || ' ' ||
            'AND ai.bcdm = $1 ' ||
            'AND ai.sbsj = $3 ' ||
            'AND ai.team_id = $6 ' ||
            'AND ai.id = mx.ipt_id
          )
    ORDER BY mx.collect_point_id';

        -- 执行查询并返回结果
        RETURN QUERY EXECUTE sql_query
        USING previous_shift_record.shiftclassid,
              previous_shift_record.shiftclassname,
              previous_shift_record.sbsj,
              previous_shift_record.xbsj,
              previous_shift_record.dbrq,
              previous_shift_record.progid;
    ELSE
        sql_query := sql_query ||
            'AND ai.acctobj_id = $6 ' ||
            'AND ai.bcdm = $1 ' ||
            'AND ai.sbsj = $3 ' ||
            'AND ai.team_id = $7 ' ||
            'AND ai.id = mx.ipt_id
          )
    ORDER BY mx.collect_point_id';

        -- 执行查询并返回结果
        RETURN QUERY EXECUTE sql_query
        USING previous_shift_record.shiftclassid,
              previous_shift_record.shiftclassname,
              previous_shift_record.sbsj,
              previous_shift_record.xbsj,
              previous_shift_record.dbrq,
              previous_shift_record.objid,
              previous_shift_record.progid;
    END IF;

END;
$$ LANGUAGE plpgsql;

-- 使用示例：
-- 1. 基本调用（不带额外单位条件）
-- SELECT * FROM get_previous_shift_collect_data('ORG001', 'PROG001');

-- 2. 带额外单位条件的调用
-- SELECT * FROM get_previous_shift_collect_data('ORG001', 'PROG001', 'IN (''UNIT001'', ''UNIT002'')');

-- 简化版存储过程（只返回采集点数据，不包含班次信息）
CREATE OR REPLACE FUNCTION get_previous_shift_collect_data_simple(
    p_orgcode VARCHAR(200),
    p_progid VARCHAR(50),
    p_where_unit TEXT DEFAULT NULL
)
RETURNS TABLE (
    collect_point_id VARCHAR,
    ipt_id VARCHAR,
    collect_point_text VARCHAR,
    collect_point_val VARCHAR,
    input_comp_type VARCHAR,
    input_options VARCHAR,
    input_time VARCHAR,
    job_input_time VARCHAR
) AS $$
DECLARE
    sql_query TEXT;
BEGIN
    -- 构建基础查询
    sql_query := '
        SELECT
            mx.collect_point_id::VARCHAR,
            mx.ipt_id::VARCHAR,
            mx.collect_point_text::VARCHAR,
            mx.collect_point_val::VARCHAR,
            mx.input_comp_type::VARCHAR,
            mx.input_options::VARCHAR,
            mx.input_time::VARCHAR,
            mx.job_input_time::VARCHAR
        FROM acctobj_inputmx mx
        WHERE mx.tmused = 1
          AND EXISTS (
              SELECT ai.id
              FROM acctobj_input ai
              INNER JOIN (
                  -- 获取上个班次信息的子查询
                  SELECT
                      s.shiftclassid,
                      s.sbsj,
                      s.progid,
                      s.objid
                  FROM shift_data s
                  WHERE s.orgcode = $1
                    AND s.progid = $2
                    AND (
                        -- 同一天的前一个班次
                        (s.dbrq = TO_CHAR(CURRENT_DATE, ''YYYY-MM-DD'')
                         AND s.sbsj < (
                             SELECT cs.sbsj
                             FROM shift_data cs
                             WHERE cs.orgcode = s.orgcode
                               AND cs.progid = s.progid
                               AND cs.dbrq = TO_CHAR(CURRENT_DATE, ''YYYY-MM-DD'')
                               AND TO_CHAR(CURRENT_TIME, ''HH24:MI:SS'') BETWEEN cs.sbsj AND cs.xbsj
                             LIMIT 1
                         ))
                        OR
                        -- 前一天的最后一个班次
                        (s.dbrq = TO_CHAR(CURRENT_DATE - INTERVAL ''1 day'', ''YYYY-MM-DD''))
                    )
                  ORDER BY s.dbrq DESC, s.sbsj DESC
                  LIMIT 1
              ) ps ON 1=1
              WHERE ai.tmused = 1';

    -- 根据是否有额外的单位条件来构建不同的SQL
    IF p_where_unit IS NOT NULL AND LENGTH(TRIM(p_where_unit)) > 0 THEN
        sql_query := sql_query || '
                AND ai.acctobj_id ' || p_where_unit || '
                AND ai.bcdm = ps.shiftclassid
                AND ai.sbsj = ps.sbsj
                AND ai.team_id = ps.progid
                AND ai.id = mx.ipt_id
          )
        ORDER BY mx.collect_point_id';

        -- 执行查询
        RETURN QUERY EXECUTE sql_query USING p_orgcode, p_progid;
    ELSE
        sql_query := sql_query || '
                AND ai.acctobj_id = ps.objid
                AND ai.bcdm = ps.shiftclassid
                AND ai.sbsj = ps.sbsj
                AND ai.team_id = ps.progid
                AND ai.id = mx.ipt_id
          )
        ORDER BY mx.collect_point_id';

        -- 执行查询
        RETURN QUERY EXECUTE sql_query USING p_orgcode, p_progid;
    END IF;

END;
$$ LANGUAGE plpgsql;
